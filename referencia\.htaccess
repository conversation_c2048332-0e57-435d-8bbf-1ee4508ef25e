# ----------------------------------------------------------------------
# <AUTHOR> E.I.R.L. <<EMAIL>>
# @link https://easyforms.dev/ Easy Forms
# ----------------------------------------------------------------------

# Don't show directory listings for URLs which map to a directory.
<IfModule mod_autoindex.c>
  Options -Indexes
</IfModule>

# Set the default handler.
<IfModule mod_dir.c>
  DirectoryIndex index.php index.html index.htm
</IfModule>

# Disable Mod Security
<IfModule mod_security.c>
  SecFilterEngine Off
  SecFilterScanPOST Off
</IfModule>

# Block access to "hidden" directories whose names begin with a period. This
# includes directories used by version control systems such as Subversion or Git.
<IfModule mod_rewrite.c>
  RewriteCond %{SCRIPT_FILENAME} -d
  RewriteCond %{SCRIPT_FILENAME} -f
  RewriteRule "(^|/)\." - [F]
</IfModule>

# Block access to backup and source files
<FilesMatch "(\.(bak|bat|config|sql|yii|yml|fla|make|md|po|sh|.*sql|ini|log|inc|swp|dist|editorconfig|gitattributes)|~|init|composer\.(json|lock)|web\.config)$">
  <IfModule mod_authz_core.c>
      Require all denied
    </IfModule>
    <IfModule !mod_authz_core.c>
      Order allow,deny
    </IfModule>
</FilesMatch>

# Increase cookie security
<IfModule php5_module>
  php_value session.cookie_httponly true
</IfModule>

# Add correct encoding for SVGZ
<IfModule mod_headers.c>
  AddType image/svg+xml svg svgz
  AddEncoding gzip svgz
</IfModule>

# Various rewrite rules.
<IfModule mod_rewrite.c>

  # Reset rewrite rules.
  RewriteEngine Off
  RewriteEngine On

  # Set "protossl" to "s" if we were accessed via https://.  This is used later
  # if you enable "www." stripping or enforcement, in order to ensure that
  # you don't bounce between http and https.
  RewriteRule ^ - [E=protossl]
  RewriteCond %{HTTPS} on
  RewriteRule ^ - [E=protossl:s]

  # Make sure Authorization HTTP header is available to PHP
  # even when running as CGI or FastCGI.
  RewriteRule ^ - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

  # If your site is at http://example.com/app uncomment the following line:
  # RewriteBase /app

  # if a directory or a file exists, use it directly
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d

  # otherwise forward it to index.php
  RewriteRule . index.php

  # For security reasons, deny access to other PHP files on public sites.
  # Allow access to install.php and license.php
  RewriteCond %{REQUEST_URI} !/install.php$
  # Allow access to PHP files in /custom (like saveToDB.php or saveToFile.php):
  # RewriteCond %{REQUEST_URI} !/custom/[^/]*\.php$
  # Deny access to any other PHP files that do not match the rules above.
  # Specifically, disallow autoload.php from being served directly.
  RewriteRule "^(.+/.*|autoload)\.php($|/)" - [F]
  # Disallow access to .git folder
  RewriteRule ^.git/ - [F]

  # Rules to correctly serve gzip compressed CSS and JS files.
  # Requires both mod_rewrite and mod_headers to be enabled.
  <IfModule mod_headers.c>
    # Serve gzip compressed CSS files if they exist and the client accepts gzip.
    RewriteCond %{HTTP:Accept-encoding} gzip
    RewriteCond %{REQUEST_FILENAME}\.gz -s
    RewriteRule ^(.*)\.css $1\.css\.gz [QSA]

    # Serve gzip compressed JS files if they exist and the client accepts gzip.
    RewriteCond %{HTTP:Accept-encoding} gzip
    RewriteCond %{REQUEST_FILENAME}\.gz -s
    RewriteRule ^(.*)\.js $1\.js\.gz [QSA]

    # Serve correct content types, and prevent mod_deflate double gzip.
    RewriteRule \.css\.gz$ - [T=text/css,E=no-gzip:1]
    RewriteRule \.js\.gz$ - [T=text/javascript,E=no-gzip:1]

    <FilesMatch "(\.js\.gz|\.css\.gz)$">
      # Serve correct encoding type.
      Header set Content-Encoding gzip
      # Force proxies to cache gzipped & non-gzipped css/js files separately.
      Header append Vary Accept-Encoding
    </FilesMatch>
  </IfModule>
</IfModule>

# Add headers to all responses.
<IfModule mod_headers.c>
  # Disable content sniffing, since it's an attack vector.
  Header always set X-Content-Type-Options nosniff
  # Disable Proxy header, since it's an attack vector.
  RequestHeader unset Proxy
  # Browsers should be allowed to render a page in a <frame>
  Header always unset X-Frame-Options
</IfModule>