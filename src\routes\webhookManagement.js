const express = require("express");
const database = require("../config/database");
const { logger } = require("../utils/logger");
const webhookService = require("../services/webhookService");

const router = express.Router();

// Função asyncHandler local
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Listar logs de webhooks
router.get(
  "/logs",
  asyncHandler(async (req, res) => {
    const {
      page = 1,
      limit = 20,
      status,
      type,
      startDate,
      endDate,
    } = req.query;

    const skip = (page - 1) * limit;
    const where = {};

    if (status) where.status = status;
    if (type) where.type = type;
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = new Date(startDate);
      if (endDate) where.createdAt.lte = new Date(endDate);
    }

    const [logs, total] = await Promise.all([
      database.getClient().webhookLog.findMany({
        where,
        orderBy: { createdAt: "desc" },
        skip: parseInt(skip),
        take: parseInt(limit),
      }),
      database.getClient().webhookLog.count({ where }),
    ]);

    res.json({
      logs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit),
      },
    });
  })
);

// Reenviar um log específico
router.post(
  "/logs/:id/retry",
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    const log = await database
      .getClient()
      .webhookLog.findUnique({ where: { id } });
    if (!log) {
      return res.status(404).json({ error: "Log de webhook não encontrado" });
    }
    if (log.status === "success") {
      return res
        .status(400)
        .json({ error: "Webhook já foi enviado com sucesso" });
    }

    try {
      const result = await webhookService.sendWebhookWithRetry(
        log.url,
        log.payload,
        {
          type: log.type || "retry",
          responseId: log.responseId || null,
        }
      );

      await database.getClient().webhookLog.update({
        where: { id },
        data: {
          status: "success",
          response: result?.data ?? result,
          error: null,
          updatedAt: new Date(),
        },
      });

      res.json({ success: true, message: "Reenvio realizado", id });
    } catch (error) {
      await database.getClient().webhookLog.update({
        where: { id },
        data: {
          status: "failed",
          error: error.message,
          updatedAt: new Date(),
        },
      });

      logger.error("Erro ao reenviar webhook:", error);
      res.status(500).json({ success: false, error: error.message });
    }
  })
);

// Limpar logs antigos
router.delete(
  "/logs/cleanup",
  asyncHandler(async (req, res) => {
    const { daysOld = 30 } = req.body || {};
    const cutoff = new Date();
    cutoff.setDate(cutoff.getDate() - parseInt(daysOld, 10));

    const deleted = await database.getClient().webhookLog.deleteMany({
      where: { createdAt: { lt: cutoff } },
    });

    res.json({
      success: true,
      deleted: deleted.count,
      cutoff,
    });
  })
);

// Estatísticas de webhooks
router.get(
  "/stats",
  asyncHandler(async (req, res) => {
    const { period = "24h" } = req.query;

    let startDate;
    switch (period) {
      case "1h":
        startDate = new Date(Date.now() - 60 * 60 * 1000);
        break;
      case "24h":
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case "7d":
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
    }

    const [total, successful, failed, byType] = await Promise.all([
      database.getClient().webhookLog.count({
        where: { createdAt: { gte: startDate } },
      }),
      database.getClient().webhookLog.count({
        where: {
          createdAt: { gte: startDate },
          status: "success",
        },
      }),
      database.getClient().webhookLog.count({
        where: {
          createdAt: { gte: startDate },
          status: "failed",
        },
      }),
      database.getClient().webhookLog.groupBy({
        by: ["type"],
        where: { createdAt: { gte: startDate } },
        _count: { type: true },
      }),
    ]);

    const successRate = total > 0 ? Math.round((successful / total) * 100) : 0;

    res.json({
      period,
      total,
      successful,
      failed,
      successRate,
      byType: byType.map((item) => ({
        type: item.type,
        count: item._count.type,
      })),
      summary: {
        total,
        successful,
        failed,
        successRate,
      },
    });
  })
);

// Obter configuração de webhooks
router.get(
  "/config",
  asyncHandler(async (req, res) => {
    try {
      // Config real baseada em variáveis de ambiente (pode ser estendida para persistência)
      const n8nWebhookUrl = process.env.EXTERNAL_N8N_WEBHOOK_URL || null;
      const webhooksEnabled = Boolean(n8nWebhookUrl);

      res.json({
        n8nWebhookUrl,
        webhooksEnabled,
        maxRetries: parseInt(process.env.WEBHOOK_MAX_RETRIES || "3", 10),
        timeout: parseInt(process.env.WEBHOOK_TIMEOUT_MS || "30000", 10),
        supportedEvents: [
          "survey_response",
          "detractor",
          "promoter",
          "conversation_started",
          "conversation_ended",
        ],
        signatureHeader: "X-UNIFORMS-Signature",
        timestampHeader: "X-UNIFORMS-Timestamp",
      });
    } catch (error) {
      logger.error("Erro ao obter configuração de webhooks:", error);
      res.status(500).json({
        error: "Erro ao obter configuração de webhooks",
        details: error.message,
      });
    }
  })
);

// Testar webhook
router.post(
  "/test",
  asyncHandler(async (req, res) => {
    const { url, payload } = req.body;

    if (!url) {
      return res.status(400).json({
        error: "URL é obrigatória",
      });
    }

    const testPayload = payload || {
      event: "test_webhook",
      timestamp: new Date().toISOString(),
      data: {
        message: "Este é um webhook de teste do UNIFORMS2",
        source: "webhook_management",
      },
    };

    try {
      const result = await webhookService.sendWebhookWithRetry(
        url,
        testPayload,
        {
          type: "test",
          responseId: null,
        }
      );

      res.json({
        success: true,
        message: "Webhook de teste enviado com sucesso",
        result,
      });
    } catch (error) {
      logger.error("Erro ao enviar webhook de teste:", error);
      res.status(500).json({
        success: false,
        error: "Erro ao enviar webhook de teste",
        details: error.message,
      });
    }
  })
);

module.exports = router;
