<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- Don't show directory listings for URLs which map to a directory. -->
        <directoryBrowse enabled="false" />

        <rewrite>
            <rules>
                <rule name="Deny access to anything beginning with ." stopProcessing="true">
                    <match url="(^|/)\..*" ignoreCase="false" />
                    <conditions logicalGrouping="MatchAny">
                        <add input="{SCRIPT_FILENAME}" matchType="IsDirectory" />
                        <add input="{SCRIPT_FILENAME}" matchType="IsFile" />
                    </conditions>
                    <action type="CustomResponse" statusCode="403" subStatusCode="0" statusReason="Forbidden" statusDescription="Access is forbidden." />
                </rule>
                <rule name="Protect files and directories from prying eyes" stopProcessing="true">
                    <match url="\.(lock|htaccess|.md|po|sh|.*sql|git|gitignore|bat|tpl(\.php)?|xtmpl|yml|svn-base)$|^(code-style\.pl|config)|vendor|yii$" />
                    <action type="CustomResponse" statusCode="403" subStatusCode="0" statusReason="Forbidden" statusDescription="Access is forbidden." />
                </rule>
                <rule name="Force simple error message for requests for non-existent favicon.ico" stopProcessing="true">
                    <match url="favicon\.ico" />
                    <action type="CustomResponse" statusCode="404" subStatusCode="1" statusReason="File Not Found" statusDescription="The requested file favicon.ico was not found" />
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                    </conditions>
                </rule>
                <rule name="Redirect missing urls to index.php" stopProcessing="true">
                    <match url="(.*)" ignoreCase="false" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="index.php" appendQueryString="true" />
                </rule>
            </rules>
        </rewrite>

        <httpProtocol>
            <customHeaders>
                <add name="Access-Control-Allow-Origin" value="*" />
                <remove name="X-Frame-Options" />
            </customHeaders>
        </httpProtocol>

        <staticContent>
            <remove fileExtension=".woff" /> <!-- In case IIS already has this mime type -->
            <mimeMap fileExtension=".woff" mimeType="font/woff" />
            <remove fileExtension=".woff2" /> <!-- In case IIS already has this mime type -->
            <mimeMap fileExtension=".woff2" mimeType="font/woff2" />
            <remove fileExtension=".json" /> <!-- In case IIS already has this mime type -->
            <mimeMap fileExtension=".json" mimeType="application/json" />
        </staticContent>

        <defaultDocument>
            <files>
                <clear />
                <add value="index.php" />
            </files>
        </defaultDocument>
    </system.webServer>
</configuration>