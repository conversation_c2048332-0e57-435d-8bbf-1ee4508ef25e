{"name": "fslightbox", "version": "3.4.1", "description": "An easy to use vanilla JavaScript plug-in without production dependencies for displaying images, videos, or, through custom sources, anything you want in a clean overlying box.", "author": "Bantha Apps Piotr Zdziarski", "license": "MIT", "homepage": "https://fslightbox.com", "bugs": {"url": "https://github.com/banthagroup/fslightbox/issues"}, "main": "index.js", "keywords": ["fslightbox", "vanilla javascript fslightbox", "vanilla js fslightbox", "vanilla javascript lightbox", "vanilla js lightbox", "lightbox"], "repository": {"type": "git", "url": "git+https://github.com/banthagroup/fslightbox"}, "scripts": {"w": "webpack-dev-server --host 0.0.0.0", "p": "webpack --config webpack.prod.config.js && cp index.js fslightbox.js"}, "devDependencies": {"@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4", "@babel/register": "^7.4.4", "babel-loader": "^8.0.5", "html-webpack-plugin": "^3.2.0", "webpack": "^4.30.0", "webpack-cli": "^3.3.1", "webpack-dev-server": "^3.3.1"}}