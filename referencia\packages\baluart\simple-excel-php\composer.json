{"name": "baluart/simple-excel-php", "type": "library", "description": "Easily parse / convert / write between Microsoft Excel XML / CSV / TSV / HTML / JSON / etc formats", "keywords": ["excel", "spreadsheet", "document", "xml", "csv", "tsv", "html", "json", "parser", "converter", "writer"], "homepage": "http://faisalman.github.com/simple-excel-php", "repositories": [{"type": "vcs", "url": "https://github.com/faisalman/simple-excel-php.git"}], "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "fyz<PERSON>@gmail.com", "homepage": "http://faisalman.com", "role": "Developer"}], "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "autoload": {"psr-0": {"SimpleExcel\\": "src/"}}}