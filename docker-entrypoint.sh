#!/bin/sh
set -e

echo "[entrypoint] Starting backend entrypoint..."

# Wait for Postgres if wait-for-it is available; fallback to simple retry
DB_HOST=${DB_HOST:-postgres}
DB_PORT=${DB_PORT:-5432}

if [ -x "./wait-for-it.sh" ]; then
  echo "[entrypoint] Waiting for database at ${DB_HOST}:${DB_PORT}..."
  ./wait-for-it.sh "${DB_HOST}:${DB_PORT}" -t 60
else
  echo "[entrypoint] wait-for-it.sh not found, doing simple wait..."
  i=0
  until nc -z ${DB_HOST} ${DB_PORT} || [ $i -ge 30 ]; do
    i=$((i+1))
    echo "[entrypoint] Waiting for DB... (${i})"
    sleep 2
  done
fi

echo "[entrypoint] Running Prisma generate (safe to skip if already generated)..."
npx prisma generate || true

echo "[entrypoint] Applying Prisma migrations (deploy)..."
if ! npx prisma migrate deploy; then
  echo "[entrypoint] migrate deploy failed or no migrations found; attempting prisma db push..."
  npx prisma db push --accept-data-loss || true
fi

if [ "${SEED_ON_START}" = "true" ]; then
  echo "[entrypoint] Running database seed..."
  node prisma/seed.js || {
    echo "[entrypoint] Seed failed" >&2
    exit 1
  }
else
  echo "[entrypoint] Skipping seed (SEED_ON_START != true)"
fi

echo "[entrypoint] Starting application (node server.js)"
exec node server.js
