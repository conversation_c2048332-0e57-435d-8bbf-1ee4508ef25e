# Nginx Proxy Manager (NPM)

This compose starts Nginx Proxy Manager to front all your services (Chatwoot, UNIFORMS backend/frontend, etc.).

- Admin UI: http://<host>:81
- Default credentials (first run):
  - Email: <EMAIL>
  - Password: changeme

You should change credentials at first login.

## Usage

- Ensure the external Docker network exists:

```sh
docker network create uniqsuporte || true
```

- Start NPM:

```sh
docker compose up -d
```

## Mapping services

Add "Proxy Hosts" in the NPM UI, pointing to containers in the same `uniqsuporte` network:

- chatw.uniqsolutions.com.br → http://chatwoot-rails-1:3000
- uniforms.uniqsuporte.com.br → http://uniforms-frontend:80 (or your frontend service)
- api.uniforms.local → http://uniforms-backend:3000

Enable "Websockets Support" and "Block Common Exploits". Issue Let's Encrypt certificates via NPM for each host.

## Data

- Persistent data: `./data` and `./letsencrypt`.

