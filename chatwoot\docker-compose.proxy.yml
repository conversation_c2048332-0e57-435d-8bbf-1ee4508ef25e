version: "3.8"

services:
  nginx-proxy:
    image: nginx:alpine
    container_name: chatwoot-nginx-proxy
    networks:
      - uniqsuporte
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./proxy/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./proxy/conf.d:/etc/nginx/conf.d:ro
      - ./proxy/certs:/etc/nginx/certs:ro
      - ./proxy/logs:/var/log/nginx
    environment:
      - TZ=America/Sao_Paulo
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "nginx -t || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 5
      start_period: 10s

networks:
  uniqsuporte:
    external: true
