version: "3.8"

services:
  npm-app:
    image: jc21/nginx-proxy-manager:latest
    container_name: npm-app
    restart: unless-stopped
    networks:
      - uniqsuporte
    ports:
      - "80:80"
      - "81:81"   # admin UI
      - "443:443"
    environment:
      TZ: America/Sao_Paulo
      # Optional if using remote DB
      # DB_MYSQL_HOST: db
      # DB_MYSQL_PORT: 3306
      # DB_MYSQL_USER: npm
      # DB_MYSQL_PASSWORD: strongpassword
      # DB_MYSQL_NAME: npm
    volumes:
      - ./data:/data
      - ./letsencrypt:/etc/letsencrypt

networks:
  uniqsuporte:
    external: true
