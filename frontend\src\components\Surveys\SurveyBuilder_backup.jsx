import React, { useState } from "react";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";

const SurveyBuilder = ({ survey, onSave, onCancel, isEditing = false }) => {
  const [formData, setFormData] = useState({
    title: survey?.title || "",
    description: survey?.description || "",
    questions: survey?.questions || [],
    settings: {
      allowMultipleSubmissions: false,
      showProgressBar: true,
      randomizeQuestions: false,
      enableSaveAndContinue: false,
    },
  });

  const [activeTab, setActiveTab] = useState("questions");

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Editor Panel */}
      <div className="w-1/2 p-6 overflow-y-auto bg-white border-r">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {isEditing ? "Editar Pesquisa" : "Nova Pesquisa"}
          </h2>
          <p className="text-gray-600">
            Configure sua pesquisa e veja o preview em tempo real
          </p>
        </div>

        {/* Tabs Navigation */}
        <div className="mb-6">
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            {[
              { id: "questions", label: "Perguntas", icon: "❓" },
              { id: "design", label: "Design", icon: "🎨" },
              { id: "logic", label: "Lógica", icon: "⚡" },
              { id: "settings", label: "Configurações", icon: "⚙️" },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? "bg-white text-blue-600 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Informações Básicas - sempre visível */}
          <div className="space-y-4 pb-6 border-b">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Título da Pesquisa *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, title: e.target.value }))
                }
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Ex: Pesquisa de Satisfação"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Descrição
              </label>
              <textarea
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows="3"
                placeholder="Descreva o objetivo da pesquisa..."
              />
            </div>
          </div>

          {/* Tab Content */}
          {activeTab === "questions" && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Perguntas da Pesquisa
                </h3>
                <p className="text-gray-600">
                  Adicione e configure as perguntas da sua pesquisa.
                </p>
              </div>
            </div>
          )}

          {activeTab === "design" && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Design da Pesquisa
                </h3>
                <p className="text-gray-600">
                  Personalize a aparência da sua pesquisa.
                </p>
              </div>
            </div>
          )}

          {activeTab === "logic" && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Lógica Condicional
                </h3>
                <p className="text-gray-600">
                  Configure regras condicionais para suas perguntas.
                </p>
              </div>
            </div>
          )}

          {activeTab === "settings" && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Configurações Avançadas
                </h3>
                <p className="text-gray-600">
                  Configure opções avançadas da pesquisa.
                </p>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center space-x-4 pt-6 border-t">
            <button
              type="submit"
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {isEditing ? "Atualizar Pesquisa" : "Criar Pesquisa"}
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancelar
            </button>
          </div>
        </form>
      </div>

      {/* Preview Panel */}
      <div className="w-1/2 p-6 bg-gray-100">
        <div className="mb-6">
          <h3 className="text-xl font-bold text-gray-900">
            Preview da Pesquisa
          </h3>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h4 className="text-lg font-medium text-gray-900 mb-2">
            {formData.title || "Título da Pesquisa"}
          </h4>
          <p className="text-gray-600 mb-4">
            {formData.description || "Descrição da pesquisa"}
          </p>
        </div>
      </div>
    </div>
  );
};

export default SurveyBuilder;
