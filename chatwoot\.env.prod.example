# Chatwoot production example
# Core
RAILS_ENV=production
NODE_ENV=production
INSTALLATION_ENV=docker
FORCE_SSL=true
DEFAULT_LOCALE=pt_BR
FRONTEND_URL=https://chatw.uniqsolutions.com.br
ENABLE_ACCOUNT_SIGNUP=false
TZ=America/Sao_Paulo

# Secrets (generate securely!)
SECRET_KEY_BASE=
LOCKBOX_MASTER_KEY=
RAILS_MASTER_KEY=

# Database
POSTGRES_HOST=postgres_geral
POSTGRES_PORT=5432
POSTGRES_DB=chatwoot_production
POSTGRES_USERNAME=chatwoot
POSTGRES_PASSWORD=
DATABASE_POOL=10

# Redis
REDIS_URL=redis://user:pass@redis_cache:6379

# Mailer (Gmail example)
SMTP_ADDRESS=smtp.gmail.com
SMTP_PORT=465
SMTP_SSL=true
SMTP_ENABLE_STARTTLS_AUTO=false
SMTP_AUTHENTICATION=login
SMTP_OPENSSL_VERIFY_MODE=none
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_DOMAIN=gmail.com
MAILER_SENDER_EMAIL="Suporte <<EMAIL>>"

# Storage
ACTIVE_STORAGE_SERVICE=local

# Logs
RAILS_LOG_TO_STDOUT=true
LOG_LEVEL=info
LOG_SIZE=500
RAILS_SERVE_STATIC_FILES=true
DISABLE_X_HEADERS=true

# Node tuning
NODE_OPTIONS=--max-old-space-size=4096
WEB_CONCURRENCY=2
RAILS_MAX_THREADS=5
