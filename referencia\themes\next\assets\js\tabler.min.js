/*!
 * Tabler v1.2.0 (https://tabler.io)
 * Copyright 2018-2025 The Tabler Authors
 * Copyright 2018-2025 codecalm.net Paweł Kuna
 * Licensed under MIT (https://github.com/tabler/tabler/blob/master/LICENSE)
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).tabler={})}(this,(function(t){"use strict";const e=document.querySelectorAll('[data-bs-toggle="autosize"]');e.length&&e.forEach((function(t){window.autosize&&window.autosize(t)}));const i=document.querySelectorAll("[data-countup]");i.length&&i.forEach((function(t){let e={};try{const i=t.getAttribute("data-countup")?JSON.parse(t.getAttribute("data-countup")):{};e=Object.assign({enableScrollSpy:!0},i)}catch(t){}const i=parseInt(t.innerHTML,10),n=new window.countUp.CountUp(t,i,e);n.error||n.start()})),[].slice.call(document.querySelectorAll("[data-mask]")).map((function(t){window.IMask&&new window.IMask(t,{mask:t.dataset.mask,lazy:"true"===t.dataset["mask-visible"]})}));var n="top",s="bottom",o="right",r="left",a="auto",l=[n,s,o,r],c="start",h="end",u="clippingParents",d="viewport",f="popper",p="reference",m=l.reduce((function(t,e){return t.concat([e+"-"+c,e+"-"+h])}),[]),g=[].concat(l,[a]).reduce((function(t,e){return t.concat([e,e+"-"+c,e+"-"+h])}),[]),_="beforeRead",b="read",v="afterRead",y="beforeMain",w="main",A="afterMain",E="beforeWrite",T="write",C="afterWrite",O=[_,b,v,y,w,A,E,T,C];function x(t){return t?(t.nodeName||"").toLowerCase():null}function k(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function S(t){return t instanceof k(t).Element||t instanceof Element}function L(t){return t instanceof k(t).HTMLElement||t instanceof HTMLElement}function $(t){return"undefined"!=typeof ShadowRoot&&(t instanceof k(t).ShadowRoot||t instanceof ShadowRoot)}const D={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var e=t.state;Object.keys(e.elements).forEach((function(t){var i=e.styles[t]||{},n=e.attributes[t]||{},s=e.elements[t];L(s)&&x(s)&&(Object.assign(s.style,i),Object.keys(n).forEach((function(t){var e=n[t];!1===e?s.removeAttribute(t):s.setAttribute(t,!0===e?"":e)})))}))},effect:function(t){var e=t.state,i={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,i.popper),e.styles=i,e.elements.arrow&&Object.assign(e.elements.arrow.style,i.arrow),function(){Object.keys(e.elements).forEach((function(t){var n=e.elements[t],s=e.attributes[t]||{},o=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:i[t]).reduce((function(t,e){return t[e]="",t}),{});L(n)&&x(n)&&(Object.assign(n.style,o),Object.keys(s).forEach((function(t){n.removeAttribute(t)})))}))}},requires:["computeStyles"]};function I(t){return t.split("-")[0]}var P=Math.max,N=Math.min,M=Math.round;function j(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map((function(t){return t.brand+"/"+t.version})).join(" "):navigator.userAgent}function F(){return!/^((?!chrome|android).)*safari/i.test(j())}function H(t,e,i){void 0===e&&(e=!1),void 0===i&&(i=!1);var n=t.getBoundingClientRect(),s=1,o=1;e&&L(t)&&(s=t.offsetWidth>0&&M(n.width)/t.offsetWidth||1,o=t.offsetHeight>0&&M(n.height)/t.offsetHeight||1);var r=(S(t)?k(t):window).visualViewport,a=!F()&&i,l=(n.left+(a&&r?r.offsetLeft:0))/s,c=(n.top+(a&&r?r.offsetTop:0))/o,h=n.width/s,u=n.height/o;return{width:h,height:u,top:c,right:l+h,bottom:c+u,left:l,x:l,y:c}}function z(t){var e=H(t),i=t.offsetWidth,n=t.offsetHeight;return Math.abs(e.width-i)<=1&&(i=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:t.offsetLeft,y:t.offsetTop,width:i,height:n}}function B(t,e){var i=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(i&&$(i)){var n=e;do{if(n&&t.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function W(t){return k(t).getComputedStyle(t)}function q(t){return["table","td","th"].indexOf(x(t))>=0}function R(t){return((S(t)?t.ownerDocument:t.document)||window.document).documentElement}function V(t){return"html"===x(t)?t:t.assignedSlot||t.parentNode||($(t)?t.host:null)||R(t)}function K(t){return L(t)&&"fixed"!==W(t).position?t.offsetParent:null}function U(t){for(var e=k(t),i=K(t);i&&q(i)&&"static"===W(i).position;)i=K(i);return i&&("html"===x(i)||"body"===x(i)&&"static"===W(i).position)?e:i||function(t){var e=/firefox/i.test(j());if(/Trident/i.test(j())&&L(t)&&"fixed"===W(t).position)return null;var i=V(t);for($(i)&&(i=i.host);L(i)&&["html","body"].indexOf(x(i))<0;){var n=W(i);if("none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||-1!==["transform","perspective"].indexOf(n.willChange)||e&&"filter"===n.willChange||e&&n.filter&&"none"!==n.filter)return i;i=i.parentNode}return null}(t)||e}function Q(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function X(t,e,i){return P(t,N(e,i))}function Y(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function G(t,e){return e.reduce((function(e,i){return e[i]=t,e}),{})}const J={name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,i=t.state,a=t.name,c=t.options,h=i.elements.arrow,u=i.modifiersData.popperOffsets,d=I(i.placement),f=Q(d),p=[r,o].indexOf(d)>=0?"height":"width";if(h&&u){var m=function(t,e){return Y("number"!=typeof(t="function"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:G(t,l))}(c.padding,i),g=z(h),_="y"===f?n:r,b="y"===f?s:o,v=i.rects.reference[p]+i.rects.reference[f]-u[f]-i.rects.popper[p],y=u[f]-i.rects.reference[f],w=U(h),A=w?"y"===f?w.clientHeight||0:w.clientWidth||0:0,E=v/2-y/2,T=m[_],C=A-g[p]-m[b],O=A/2-g[p]/2+E,x=X(T,O,C),k=f;i.modifiersData[a]=((e={})[k]=x,e.centerOffset=x-O,e)}},effect:function(t){var e=t.state,i=t.options.element,n=void 0===i?"[data-popper-arrow]":i;null!=n&&("string"!=typeof n||(n=e.elements.popper.querySelector(n)))&&B(e.elements.popper,n)&&(e.elements.arrow=n)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Z(t){return t.split("-")[1]}var tt={top:"auto",right:"auto",bottom:"auto",left:"auto"};function et(t){var e,i=t.popper,a=t.popperRect,l=t.placement,c=t.variation,u=t.offsets,d=t.position,f=t.gpuAcceleration,p=t.adaptive,m=t.roundOffsets,g=t.isFixed,_=u.x,b=void 0===_?0:_,v=u.y,y=void 0===v?0:v,w="function"==typeof m?m({x:b,y:y}):{x:b,y:y};b=w.x,y=w.y;var A=u.hasOwnProperty("x"),E=u.hasOwnProperty("y"),T=r,C=n,O=window;if(p){var x=U(i),S="clientHeight",L="clientWidth";x===k(i)&&"static"!==W(x=R(i)).position&&"absolute"===d&&(S="scrollHeight",L="scrollWidth"),(l===n||(l===r||l===o)&&c===h)&&(C=s,y-=(g&&x===O&&O.visualViewport?O.visualViewport.height:x[S])-a.height,y*=f?1:-1),l!==r&&(l!==n&&l!==s||c!==h)||(T=o,b-=(g&&x===O&&O.visualViewport?O.visualViewport.width:x[L])-a.width,b*=f?1:-1)}var $,D=Object.assign({position:d},p&&tt),I=!0===m?function(t,e){var i=t.x,n=t.y,s=e.devicePixelRatio||1;return{x:M(i*s)/s||0,y:M(n*s)/s||0}}({x:b,y:y},k(i)):{x:b,y:y};return b=I.x,y=I.y,f?Object.assign({},D,(($={})[C]=E?"0":"",$[T]=A?"0":"",$.transform=(O.devicePixelRatio||1)<=1?"translate("+b+"px, "+y+"px)":"translate3d("+b+"px, "+y+"px, 0)",$)):Object.assign({},D,((e={})[C]=E?y+"px":"",e[T]=A?b+"px":"",e.transform="",e))}const it={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,i=t.options,n=i.gpuAcceleration,s=void 0===n||n,o=i.adaptive,r=void 0===o||o,a=i.roundOffsets,l=void 0===a||a,c={placement:I(e.placement),variation:Z(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:s,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,et(Object.assign({},c,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:r,roundOffsets:l})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,et(Object.assign({},c,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}};var nt={passive:!0};const st={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,i=t.instance,n=t.options,s=n.scroll,o=void 0===s||s,r=n.resize,a=void 0===r||r,l=k(e.elements.popper),c=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&c.forEach((function(t){t.addEventListener("scroll",i.update,nt)})),a&&l.addEventListener("resize",i.update,nt),function(){o&&c.forEach((function(t){t.removeEventListener("scroll",i.update,nt)})),a&&l.removeEventListener("resize",i.update,nt)}},data:{}};var ot={left:"right",right:"left",bottom:"top",top:"bottom"};function rt(t){return t.replace(/left|right|bottom|top/g,(function(t){return ot[t]}))}var at={start:"end",end:"start"};function lt(t){return t.replace(/start|end/g,(function(t){return at[t]}))}function ct(t){var e=k(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function ht(t){return H(R(t)).left+ct(t).scrollLeft}function ut(t){var e=W(t),i=e.overflow,n=e.overflowX,s=e.overflowY;return/auto|scroll|overlay|hidden/.test(i+s+n)}function dt(t){return["html","body","#document"].indexOf(x(t))>=0?t.ownerDocument.body:L(t)&&ut(t)?t:dt(V(t))}function ft(t,e){var i;void 0===e&&(e=[]);var n=dt(t),s=n===(null==(i=t.ownerDocument)?void 0:i.body),o=k(n),r=s?[o].concat(o.visualViewport||[],ut(n)?n:[]):n,a=e.concat(r);return s?a:a.concat(ft(V(r)))}function pt(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function mt(t,e,i){return e===d?pt(function(t,e){var i=k(t),n=R(t),s=i.visualViewport,o=n.clientWidth,r=n.clientHeight,a=0,l=0;if(s){o=s.width,r=s.height;var c=F();(c||!c&&"fixed"===e)&&(a=s.offsetLeft,l=s.offsetTop)}return{width:o,height:r,x:a+ht(t),y:l}}(t,i)):S(e)?function(t,e){var i=H(t,!1,"fixed"===e);return i.top=i.top+t.clientTop,i.left=i.left+t.clientLeft,i.bottom=i.top+t.clientHeight,i.right=i.left+t.clientWidth,i.width=t.clientWidth,i.height=t.clientHeight,i.x=i.left,i.y=i.top,i}(e,i):pt(function(t){var e,i=R(t),n=ct(t),s=null==(e=t.ownerDocument)?void 0:e.body,o=P(i.scrollWidth,i.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),r=P(i.scrollHeight,i.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),a=-n.scrollLeft+ht(t),l=-n.scrollTop;return"rtl"===W(s||i).direction&&(a+=P(i.clientWidth,s?s.clientWidth:0)-o),{width:o,height:r,x:a,y:l}}(R(t)))}function gt(t){var e,i=t.reference,a=t.element,l=t.placement,u=l?I(l):null,d=l?Z(l):null,f=i.x+i.width/2-a.width/2,p=i.y+i.height/2-a.height/2;switch(u){case n:e={x:f,y:i.y-a.height};break;case s:e={x:f,y:i.y+i.height};break;case o:e={x:i.x+i.width,y:p};break;case r:e={x:i.x-a.width,y:p};break;default:e={x:i.x,y:i.y}}var m=u?Q(u):null;if(null!=m){var g="y"===m?"height":"width";switch(d){case c:e[m]=e[m]-(i[g]/2-a[g]/2);break;case h:e[m]=e[m]+(i[g]/2-a[g]/2)}}return e}function _t(t,e){void 0===e&&(e={});var i=e,r=i.placement,a=void 0===r?t.placement:r,c=i.strategy,h=void 0===c?t.strategy:c,m=i.boundary,g=void 0===m?u:m,_=i.rootBoundary,b=void 0===_?d:_,v=i.elementContext,y=void 0===v?f:v,w=i.altBoundary,A=void 0!==w&&w,E=i.padding,T=void 0===E?0:E,C=Y("number"!=typeof T?T:G(T,l)),O=y===f?p:f,k=t.rects.popper,$=t.elements[A?O:y],D=function(t,e,i,n){var s="clippingParents"===e?function(t){var e=ft(V(t)),i=["absolute","fixed"].indexOf(W(t).position)>=0&&L(t)?U(t):t;return S(i)?e.filter((function(t){return S(t)&&B(t,i)&&"body"!==x(t)})):[]}(t):[].concat(e),o=[].concat(s,[i]),r=o[0],a=o.reduce((function(e,i){var s=mt(t,i,n);return e.top=P(s.top,e.top),e.right=N(s.right,e.right),e.bottom=N(s.bottom,e.bottom),e.left=P(s.left,e.left),e}),mt(t,r,n));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}(S($)?$:$.contextElement||R(t.elements.popper),g,b,h),I=H(t.elements.reference),M=gt({reference:I,element:k,placement:a}),j=pt(Object.assign({},k,M)),F=y===f?j:I,z={top:D.top-F.top+C.top,bottom:F.bottom-D.bottom+C.bottom,left:D.left-F.left+C.left,right:F.right-D.right+C.right},q=t.modifiersData.offset;if(y===f&&q){var K=q[a];Object.keys(z).forEach((function(t){var e=[o,s].indexOf(t)>=0?1:-1,i=[n,s].indexOf(t)>=0?"y":"x";z[t]+=K[i]*e}))}return z}function bt(t,e){void 0===e&&(e={});var i=e,n=i.placement,s=i.boundary,o=i.rootBoundary,r=i.padding,a=i.flipVariations,c=i.allowedAutoPlacements,h=void 0===c?g:c,u=Z(n),d=u?a?m:m.filter((function(t){return Z(t)===u})):l,f=d.filter((function(t){return h.indexOf(t)>=0}));0===f.length&&(f=d);var p=f.reduce((function(e,i){return e[i]=_t(t,{placement:i,boundary:s,rootBoundary:o,padding:r})[I(i)],e}),{});return Object.keys(p).sort((function(t,e){return p[t]-p[e]}))}const vt={name:"flip",enabled:!0,phase:"main",fn:function(t){var e=t.state,i=t.options,l=t.name;if(!e.modifiersData[l]._skip){for(var h=i.mainAxis,u=void 0===h||h,d=i.altAxis,f=void 0===d||d,p=i.fallbackPlacements,m=i.padding,g=i.boundary,_=i.rootBoundary,b=i.altBoundary,v=i.flipVariations,y=void 0===v||v,w=i.allowedAutoPlacements,A=e.options.placement,E=I(A),T=p||(E!==A&&y?function(t){if(I(t)===a)return[];var e=rt(t);return[lt(t),e,lt(e)]}(A):[rt(A)]),C=[A].concat(T).reduce((function(t,i){return t.concat(I(i)===a?bt(e,{placement:i,boundary:g,rootBoundary:_,padding:m,flipVariations:y,allowedAutoPlacements:w}):i)}),[]),O=e.rects.reference,x=e.rects.popper,k=new Map,S=!0,L=C[0],$=0;$<C.length;$++){var D=C[$],P=I(D),N=Z(D)===c,M=[n,s].indexOf(P)>=0,j=M?"width":"height",F=_t(e,{placement:D,boundary:g,rootBoundary:_,altBoundary:b,padding:m}),H=M?N?o:r:N?s:n;O[j]>x[j]&&(H=rt(H));var z=rt(H),B=[];if(u&&B.push(F[P]<=0),f&&B.push(F[H]<=0,F[z]<=0),B.every((function(t){return t}))){L=D,S=!1;break}k.set(D,B)}if(S)for(var W=function(t){var e=C.find((function(e){var i=k.get(e);if(i)return i.slice(0,t).every((function(t){return t}))}));if(e)return L=e,"break"},q=y?3:1;q>0&&"break"!==W(q);q--);e.placement!==L&&(e.modifiersData[l]._skip=!0,e.placement=L,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function yt(t,e,i){return void 0===i&&(i={x:0,y:0}),{top:t.top-e.height-i.y,right:t.right-e.width+i.x,bottom:t.bottom-e.height+i.y,left:t.left-e.width-i.x}}function wt(t){return[n,o,s,r].some((function(e){return t[e]>=0}))}const At={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,i=t.name,n=e.rects.reference,s=e.rects.popper,o=e.modifiersData.preventOverflow,r=_t(e,{elementContext:"reference"}),a=_t(e,{altBoundary:!0}),l=yt(r,n),c=yt(a,s,o),h=wt(l),u=wt(c);e.modifiersData[i]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:h,hasPopperEscaped:u},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":h,"data-popper-escaped":u})}},Et={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var e=t.state,i=t.options,s=t.name,a=i.offset,l=void 0===a?[0,0]:a,c=g.reduce((function(t,i){return t[i]=function(t,e,i){var s=I(t),a=[r,n].indexOf(s)>=0?-1:1,l="function"==typeof i?i(Object.assign({},e,{placement:t})):i,c=l[0],h=l[1];return c=c||0,h=(h||0)*a,[r,o].indexOf(s)>=0?{x:h,y:c}:{x:c,y:h}}(i,e.rects,l),t}),{}),h=c[e.placement],u=h.x,d=h.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=u,e.modifiersData.popperOffsets.y+=d),e.modifiersData[s]=c}},Tt={name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,i=t.name;e.modifiersData[i]=gt({reference:e.rects.reference,element:e.rects.popper,placement:e.placement})},data:{}},Ct={name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e=t.state,i=t.options,a=t.name,l=i.mainAxis,h=void 0===l||l,u=i.altAxis,d=void 0!==u&&u,f=i.boundary,p=i.rootBoundary,m=i.altBoundary,g=i.padding,_=i.tether,b=void 0===_||_,v=i.tetherOffset,y=void 0===v?0:v,w=_t(e,{boundary:f,rootBoundary:p,padding:g,altBoundary:m}),A=I(e.placement),E=Z(e.placement),T=!E,C=Q(A),O="x"===C?"y":"x",x=e.modifiersData.popperOffsets,k=e.rects.reference,S=e.rects.popper,L="function"==typeof y?y(Object.assign({},e.rects,{placement:e.placement})):y,$="number"==typeof L?{mainAxis:L,altAxis:L}:Object.assign({mainAxis:0,altAxis:0},L),D=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,M={x:0,y:0};if(x){if(h){var j,F="y"===C?n:r,H="y"===C?s:o,B="y"===C?"height":"width",W=x[C],q=W+w[F],R=W-w[H],V=b?-S[B]/2:0,K=E===c?k[B]:S[B],Y=E===c?-S[B]:-k[B],G=e.elements.arrow,J=b&&G?z(G):{width:0,height:0},tt=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},et=tt[F],it=tt[H],nt=X(0,k[B],J[B]),st=T?k[B]/2-V-nt-et-$.mainAxis:K-nt-et-$.mainAxis,ot=T?-k[B]/2+V+nt+it+$.mainAxis:Y+nt+it+$.mainAxis,rt=e.elements.arrow&&U(e.elements.arrow),at=rt?"y"===C?rt.clientTop||0:rt.clientLeft||0:0,lt=null!=(j=null==D?void 0:D[C])?j:0,ct=W+ot-lt,ht=X(b?N(q,W+st-lt-at):q,W,b?P(R,ct):R);x[C]=ht,M[C]=ht-W}if(d){var ut,dt="x"===C?n:r,ft="x"===C?s:o,pt=x[O],mt="y"===O?"height":"width",gt=pt+w[dt],bt=pt-w[ft],vt=-1!==[n,r].indexOf(A),yt=null!=(ut=null==D?void 0:D[O])?ut:0,wt=vt?gt:pt-k[mt]-S[mt]-yt+$.altAxis,At=vt?pt+k[mt]+S[mt]-yt-$.altAxis:bt,Et=b&&vt?function(t,e,i){var n=X(t,e,i);return n>i?i:n}(wt,pt,At):X(b?wt:gt,pt,b?At:bt);x[O]=Et,M[O]=Et-pt}e.modifiersData[a]=M}},requiresIfExists:["offset"]};function Ot(t,e,i){void 0===i&&(i=!1);var n,s,o=L(e),r=L(e)&&function(t){var e=t.getBoundingClientRect(),i=M(e.width)/t.offsetWidth||1,n=M(e.height)/t.offsetHeight||1;return 1!==i||1!==n}(e),a=R(e),l=H(t,r,i),c={scrollLeft:0,scrollTop:0},h={x:0,y:0};return(o||!o&&!i)&&(("body"!==x(e)||ut(a))&&(c=(n=e)!==k(n)&&L(n)?{scrollLeft:(s=n).scrollLeft,scrollTop:s.scrollTop}:ct(n)),L(e)?((h=H(e,!0)).x+=e.clientLeft,h.y+=e.clientTop):a&&(h.x=ht(a))),{x:l.left+c.scrollLeft-h.x,y:l.top+c.scrollTop-h.y,width:l.width,height:l.height}}function xt(t){var e=new Map,i=new Set,n=[];function s(t){i.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach((function(t){if(!i.has(t)){var n=e.get(t);n&&s(n)}})),n.push(t)}return t.forEach((function(t){e.set(t.name,t)})),t.forEach((function(t){i.has(t.name)||s(t)})),n}var kt={placement:"bottom",modifiers:[],strategy:"absolute"};function St(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return!e.some((function(t){return!(t&&"function"==typeof t.getBoundingClientRect)}))}function Lt(t){void 0===t&&(t={});var e=t,i=e.defaultModifiers,n=void 0===i?[]:i,s=e.defaultOptions,o=void 0===s?kt:s;return function(t,e,i){void 0===i&&(i=o);var s,r,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},kt,o),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},l=[],c=!1,h={state:a,setOptions:function(i){var s="function"==typeof i?i(a.options):i;u(),a.options=Object.assign({},o,a.options,s),a.scrollParents={reference:S(t)?ft(t):t.contextElement?ft(t.contextElement):[],popper:ft(e)};var r,c,d=function(t){var e=xt(t);return O.reduce((function(t,i){return t.concat(e.filter((function(t){return t.phase===i})))}),[])}((r=[].concat(n,a.options.modifiers),c=r.reduce((function(t,e){var i=t[e.name];return t[e.name]=i?Object.assign({},i,e,{options:Object.assign({},i.options,e.options),data:Object.assign({},i.data,e.data)}):e,t}),{}),Object.keys(c).map((function(t){return c[t]}))));return a.orderedModifiers=d.filter((function(t){return t.enabled})),a.orderedModifiers.forEach((function(t){var e=t.name,i=t.options,n=void 0===i?{}:i,s=t.effect;if("function"==typeof s){var o=s({state:a,name:e,instance:h,options:n});l.push(o||function(){})}})),h.update()},forceUpdate:function(){if(!c){var t=a.elements,e=t.reference,i=t.popper;if(St(e,i)){a.rects={reference:Ot(e,U(i),"fixed"===a.options.strategy),popper:z(i)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach((function(t){return a.modifiersData[t.name]=Object.assign({},t.data)}));for(var n=0;n<a.orderedModifiers.length;n++)if(!0!==a.reset){var s=a.orderedModifiers[n],o=s.fn,r=s.options,l=void 0===r?{}:r,u=s.name;"function"==typeof o&&(a=o({state:a,options:l,name:u,instance:h})||a)}else a.reset=!1,n=-1}}},update:(s=function(){return new Promise((function(t){h.forceUpdate(),t(a)}))},function(){return r||(r=new Promise((function(t){Promise.resolve().then((function(){r=void 0,t(s())}))}))),r}),destroy:function(){u(),c=!0}};if(!St(t,e))return h;function u(){l.forEach((function(t){return t()})),l=[]}return h.setOptions(i).then((function(t){!c&&i.onFirstUpdate&&i.onFirstUpdate(t)})),h}}var $t=Lt(),Dt=Lt({defaultModifiers:[st,Tt,it,D]}),It=Lt({defaultModifiers:[st,Tt,it,D,Et,vt,Ct,J,At]});const Pt=Object.freeze(Object.defineProperty({__proto__:null,afterMain:A,afterRead:v,afterWrite:C,applyStyles:D,arrow:J,auto:a,basePlacements:l,beforeMain:y,beforeRead:_,beforeWrite:E,bottom:s,clippingParents:u,computeStyles:it,createPopper:It,createPopperBase:$t,createPopperLite:Dt,detectOverflow:_t,end:h,eventListeners:st,flip:vt,hide:At,left:r,main:w,modifierPhases:O,offset:Et,placements:g,popper:f,popperGenerator:Lt,popperOffsets:Tt,preventOverflow:Ct,read:b,reference:p,right:o,start:c,top:n,variationPlacements:m,viewport:d,write:T},Symbol.toStringTag,{value:"Module"})),Nt=new Map,Mt={set(t,e,i){Nt.has(t)||Nt.set(t,new Map);const n=Nt.get(t);n.has(e)||0===n.size?n.set(e,i):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(n.keys())[0]}.`)},get:(t,e)=>Nt.has(t)&&Nt.get(t).get(e)||null,remove(t,e){if(!Nt.has(t))return;const i=Nt.get(t);i.delete(e),0===i.size&&Nt.delete(t)}},jt="transitionend",Ft=t=>(t&&window.CSS&&window.CSS.escape&&(t=t.replace(/#([^\s"#']+)/g,((t,e)=>`#${CSS.escape(e)}`))),t),Ht=t=>{t.dispatchEvent(new Event(jt))},zt=t=>!(!t||"object"!=typeof t)&&(void 0!==t.jquery&&(t=t[0]),void 0!==t.nodeType),Bt=t=>zt(t)?t.jquery?t[0]:t:"string"==typeof t&&t.length>0?document.querySelector(Ft(t)):null,Wt=t=>{if(!zt(t)||0===t.getClientRects().length)return!1;const e="visible"===getComputedStyle(t).getPropertyValue("visibility"),i=t.closest("details:not([open])");if(!i)return e;if(i!==t){const e=t.closest("summary");if(e&&e.parentNode!==i)return!1;if(null===e)return!1}return e},qt=t=>!t||t.nodeType!==Node.ELEMENT_NODE||!!t.classList.contains("disabled")||(void 0!==t.disabled?t.disabled:t.hasAttribute("disabled")&&"false"!==t.getAttribute("disabled")),Rt=t=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof t.getRootNode){const e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?Rt(t.parentNode):null},Vt=()=>{},Kt=t=>{t.offsetHeight},Ut=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,Qt=[],Xt=()=>"rtl"===document.documentElement.dir,Yt=t=>{var e;e=()=>{const e=Ut();if(e){const i=t.NAME,n=e.fn[i];e.fn[i]=t.jQueryInterface,e.fn[i].Constructor=t,e.fn[i].noConflict=()=>(e.fn[i]=n,t.jQueryInterface)}},"loading"===document.readyState?(Qt.length||document.addEventListener("DOMContentLoaded",(()=>{for(const t of Qt)t()})),Qt.push(e)):e()},Gt=(t,e=[],i=t)=>"function"==typeof t?t.call(...e):i,Jt=(t,e,i=!0)=>{if(!i)return void Gt(t);const n=(t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:i}=window.getComputedStyle(t);const n=Number.parseFloat(e),s=Number.parseFloat(i);return n||s?(e=e.split(",")[0],i=i.split(",")[0],1e3*(Number.parseFloat(e)+Number.parseFloat(i))):0})(e)+5;let s=!1;const o=({target:i})=>{i===e&&(s=!0,e.removeEventListener(jt,o),Gt(t))};e.addEventListener(jt,o),setTimeout((()=>{s||Ht(e)}),n)},Zt=(t,e,i,n)=>{const s=t.length;let o=t.indexOf(e);return-1===o?!i&&n?t[s-1]:t[0]:(o+=i?1:-1,n&&(o=(o+s)%s),t[Math.max(0,Math.min(o,s-1))])},te=/[^.]*(?=\..*)\.|.*/,ee=/\..*/,ie=/::\d+$/,ne={};
    /*!
          * Bootstrap v5.3.5 (https://getbootstrap.com/)
          * Copyright 2011-2025 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
          * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
          */let se=1;const oe={mouseenter:"mouseover",mouseleave:"mouseout"},re=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function ae(t,e){return e&&`${e}::${se++}`||t.uidEvent||se++}function le(t){const e=ae(t);return t.uidEvent=e,ne[e]=ne[e]||{},ne[e]}function ce(t,e,i=null){return Object.values(t).find((t=>t.callable===e&&t.delegationSelector===i))}function he(t,e,i){const n="string"==typeof e,s=n?i:e||i;let o=pe(t);return re.has(o)||(o=t),[n,s,o]}function ue(t,e,i,n,s){if("string"!=typeof e||!t)return;let[o,r,a]=he(e,i,n);if(e in oe){const t=t=>function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return t.call(this,e)};r=t(r)}const l=le(t),c=l[a]||(l[a]={}),h=ce(c,r,o?i:null);if(h)return void(h.oneOff=h.oneOff&&s);const u=ae(r,e.replace(te,"")),d=o?function(t,e,i){return function n(s){const o=t.querySelectorAll(e);for(let{target:r}=s;r&&r!==this;r=r.parentNode)for(const a of o)if(a===r)return ge(s,{delegateTarget:r}),n.oneOff&&me.off(t,s.type,e,i),i.apply(r,[s])}}(t,i,r):function(t,e){return function i(n){return ge(n,{delegateTarget:t}),i.oneOff&&me.off(t,n.type,e),e.apply(t,[n])}}(t,r);d.delegationSelector=o?i:null,d.callable=r,d.oneOff=s,d.uidEvent=u,c[u]=d,t.addEventListener(a,d,o)}function de(t,e,i,n,s){const o=ce(e[i],n,s);o&&(t.removeEventListener(i,o,Boolean(s)),delete e[i][o.uidEvent])}function fe(t,e,i,n){const s=e[i]||{};for(const[o,r]of Object.entries(s))o.includes(n)&&de(t,e,i,r.callable,r.delegationSelector)}function pe(t){return t=t.replace(ee,""),oe[t]||t}const me={on(t,e,i,n){ue(t,e,i,n,!1)},one(t,e,i,n){ue(t,e,i,n,!0)},off(t,e,i,n){if("string"!=typeof e||!t)return;const[s,o,r]=he(e,i,n),a=r!==e,l=le(t),c=l[r]||{},h=e.startsWith(".");if(void 0===o){if(h)for(const i of Object.keys(l))fe(t,l,i,e.slice(1));for(const[i,n]of Object.entries(c)){const s=i.replace(ie,"");a&&!e.includes(s)||de(t,l,r,n.callable,n.delegationSelector)}}else{if(!Object.keys(c).length)return;de(t,l,r,o,s?i:null)}},trigger(t,e,i){if("string"!=typeof e||!t)return null;const n=Ut();let s=null,o=!0,r=!0,a=!1;e!==pe(e)&&n&&(s=n.Event(e,i),n(t).trigger(s),o=!s.isPropagationStopped(),r=!s.isImmediatePropagationStopped(),a=s.isDefaultPrevented());const l=ge(new Event(e,{bubbles:o,cancelable:!0}),i);return a&&l.preventDefault(),r&&t.dispatchEvent(l),l.defaultPrevented&&s&&s.preventDefault(),l}};function ge(t,e={}){for(const[i,n]of Object.entries(e))try{t[i]=n}catch(e){Object.defineProperty(t,i,{configurable:!0,get:()=>n})}return t}function _e(t){if("true"===t)return!0;if("false"===t)return!1;if(t===Number(t).toString())return Number(t);if(""===t||"null"===t)return null;if("string"!=typeof t)return t;try{return JSON.parse(decodeURIComponent(t))}catch(e){return t}}function be(t){return t.replace(/[A-Z]/g,(t=>`-${t.toLowerCase()}`))}const ve={setDataAttribute(t,e,i){t.setAttribute(`data-bs-${be(e)}`,i)},removeDataAttribute(t,e){t.removeAttribute(`data-bs-${be(e)}`)},getDataAttributes(t){if(!t)return{};const e={},i=Object.keys(t.dataset).filter((t=>t.startsWith("bs")&&!t.startsWith("bsConfig")));for(const n of i){let i=n.replace(/^bs/,"");i=i.charAt(0).toLowerCase()+i.slice(1),e[i]=_e(t.dataset[n])}return e},getDataAttribute:(t,e)=>_e(t.getAttribute(`data-bs-${be(e)}`))};class ye{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const i=zt(e)?ve.getDataAttribute(e,"config"):{};return{...this.constructor.Default,..."object"==typeof i?i:{},...zt(e)?ve.getDataAttributes(e):{},..."object"==typeof t?t:{}}}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[n,s]of Object.entries(e)){const e=t[n],o=zt(e)?"element":null==(i=e)?`${i}`:Object.prototype.toString.call(i).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(s).test(o))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${n}" provided type "${o}" but expected type "${s}".`)}var i}}class we extends ye{constructor(t,e){super(),(t=Bt(t))&&(this._element=t,this._config=this._getConfig(e),Mt.set(this._element,this.constructor.DATA_KEY,this))}dispose(){Mt.remove(this._element,this.constructor.DATA_KEY),me.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,i=!0){Jt(t,e,i)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return Mt.get(Bt(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,"object"==typeof e?e:null)}static get VERSION(){return"5.3.5"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const Ae=t=>{let e=t.getAttribute("data-bs-target");if(!e||"#"===e){let i=t.getAttribute("href");if(!i||!i.includes("#")&&!i.startsWith("."))return null;i.includes("#")&&!i.startsWith("#")&&(i=`#${i.split("#")[1]}`),e=i&&"#"!==i?i.trim():null}return e?e.split(",").map((t=>Ft(t))).join(","):null},Ee={find:(t,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,t)),findOne:(t,e=document.documentElement)=>Element.prototype.querySelector.call(e,t),children:(t,e)=>[].concat(...t.children).filter((t=>t.matches(e))),parents(t,e){const i=[];let n=t.parentNode.closest(e);for(;n;)i.push(n),n=n.parentNode.closest(e);return i},prev(t,e){let i=t.previousElementSibling;for(;i;){if(i.matches(e))return[i];i=i.previousElementSibling}return[]},next(t,e){let i=t.nextElementSibling;for(;i;){if(i.matches(e))return[i];i=i.nextElementSibling}return[]},focusableChildren(t){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((t=>`${t}:not([tabindex^="-"])`)).join(",");return this.find(e,t).filter((t=>!qt(t)&&Wt(t)))},getSelectorFromElement(t){const e=Ae(t);return e&&Ee.findOne(e)?e:null},getElementFromSelector(t){const e=Ae(t);return e?Ee.findOne(e):null},getMultipleElementsFromSelector(t){const e=Ae(t);return e?Ee.find(e):[]}},Te=(t,e="hide")=>{const i=`click.dismiss${t.EVENT_KEY}`,n=t.NAME;me.on(document,i,`[data-bs-dismiss="${n}"]`,(function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),qt(this))return;const s=Ee.getElementFromSelector(this)||this.closest(`.${n}`);t.getOrCreateInstance(s)[e]()}))},Ce=".bs.alert",Oe=`close${Ce}`,xe=`closed${Ce}`;class ke extends we{static get NAME(){return"alert"}close(){if(me.trigger(this._element,Oe).defaultPrevented)return;this._element.classList.remove("show");const t=this._element.classList.contains("fade");this._queueCallback((()=>this._destroyElement()),this._element,t)}_destroyElement(){this._element.remove(),me.trigger(this._element,xe),this.dispose()}static jQueryInterface(t){return this.each((function(){const e=ke.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}Te(ke,"close"),Yt(ke);const Se='[data-bs-toggle="button"]';class Le extends we{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(t){return this.each((function(){const e=Le.getOrCreateInstance(this);"toggle"===t&&e[t]()}))}}me.on(document,"click.bs.button.data-api",Se,(t=>{t.preventDefault();const e=t.target.closest(Se);Le.getOrCreateInstance(e).toggle()})),Yt(Le);const $e=".bs.swipe",De=`touchstart${$e}`,Ie=`touchmove${$e}`,Pe=`touchend${$e}`,Ne=`pointerdown${$e}`,Me=`pointerup${$e}`,je={endCallback:null,leftCallback:null,rightCallback:null},Fe={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class He extends ye{constructor(t,e){super(),this._element=t,t&&He.isSupported()&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return je}static get DefaultType(){return Fe}static get NAME(){return"swipe"}dispose(){me.off(this._element,$e)}_start(t){this._supportPointerEvents?this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX):this._deltaX=t.touches[0].clientX}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),Gt(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=40)return;const e=t/this._deltaX;this._deltaX=0,e&&Gt(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(me.on(this._element,Ne,(t=>this._start(t))),me.on(this._element,Me,(t=>this._end(t))),this._element.classList.add("pointer-event")):(me.on(this._element,De,(t=>this._start(t))),me.on(this._element,Ie,(t=>this._move(t))),me.on(this._element,Pe,(t=>this._end(t))))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&("pen"===t.pointerType||"touch"===t.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const ze=".bs.carousel",Be=".data-api",We="ArrowLeft",qe="ArrowRight",Re="next",Ve="prev",Ke="left",Ue="right",Qe=`slide${ze}`,Xe=`slid${ze}`,Ye=`keydown${ze}`,Ge=`mouseenter${ze}`,Je=`mouseleave${ze}`,Ze=`dragstart${ze}`,ti=`load${ze}${Be}`,ei=`click${ze}${Be}`,ii="carousel",ni="active",si=".active",oi=".carousel-item",ri=si+oi,ai={[We]:Ue,[qe]:Ke},li={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},ci={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class hi extends we{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=Ee.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===ii&&this.cycle()}static get Default(){return li}static get DefaultType(){return ci}static get NAME(){return"carousel"}next(){this._slide(Re)}nextWhenVisible(){!document.hidden&&Wt(this._element)&&this.next()}prev(){this._slide(Ve)}pause(){this._isSliding&&Ht(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval((()=>this.nextWhenVisible()),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?me.one(this._element,Xe,(()=>this.cycle())):this.cycle())}to(t){const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding)return void me.one(this._element,Xe,(()=>this.to(t)));const i=this._getItemIndex(this._getActive());if(i===t)return;const n=t>i?Re:Ve;this._slide(n,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&me.on(this._element,Ye,(t=>this._keydown(t))),"hover"===this._config.pause&&(me.on(this._element,Ge,(()=>this.pause())),me.on(this._element,Je,(()=>this._maybeEnableCycle()))),this._config.touch&&He.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of Ee.find(".carousel-item img",this._element))me.on(t,Ze,(t=>t.preventDefault()));const t={leftCallback:()=>this._slide(this._directionToOrder(Ke)),rightCallback:()=>this._slide(this._directionToOrder(Ue)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((()=>this._maybeEnableCycle()),500+this._config.interval))}};this._swipeHelper=new He(this._element,t)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=ai[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=Ee.findOne(si,this._indicatorsElement);e.classList.remove(ni),e.removeAttribute("aria-current");const i=Ee.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);i&&(i.classList.add(ni),i.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=e||this._config.defaultInterval}_slide(t,e=null){if(this._isSliding)return;const i=this._getActive(),n=t===Re,s=e||Zt(this._getItems(),i,n,this._config.wrap);if(s===i)return;const o=this._getItemIndex(s),r=e=>me.trigger(this._element,e,{relatedTarget:s,direction:this._orderToDirection(t),from:this._getItemIndex(i),to:o});if(r(Qe).defaultPrevented)return;if(!i||!s)return;const a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=s;const l=n?"carousel-item-start":"carousel-item-end",c=n?"carousel-item-next":"carousel-item-prev";s.classList.add(c),Kt(s),i.classList.add(l),s.classList.add(l),this._queueCallback((()=>{s.classList.remove(l,c),s.classList.add(ni),i.classList.remove(ni,c,l),this._isSliding=!1,r(Xe)}),i,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return Ee.findOne(ri,this._element)}_getItems(){return Ee.find(oi,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return Xt()?t===Ke?Ve:Re:t===Ke?Re:Ve}_orderToDirection(t){return Xt()?t===Ve?Ke:Ue:t===Ve?Ue:Ke}static jQueryInterface(t){return this.each((function(){const e=hi.getOrCreateInstance(this,t);if("number"!=typeof t){if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}else e.to(t)}))}}me.on(document,ei,"[data-bs-slide], [data-bs-slide-to]",(function(t){const e=Ee.getElementFromSelector(this);if(!e||!e.classList.contains(ii))return;t.preventDefault();const i=hi.getOrCreateInstance(e),n=this.getAttribute("data-bs-slide-to");return n?(i.to(n),void i._maybeEnableCycle()):"next"===ve.getDataAttribute(this,"slide")?(i.next(),void i._maybeEnableCycle()):(i.prev(),void i._maybeEnableCycle())})),me.on(window,ti,(()=>{const t=Ee.find('[data-bs-ride="carousel"]');for(const e of t)hi.getOrCreateInstance(e)})),Yt(hi);const ui=".bs.collapse",di=`show${ui}`,fi=`shown${ui}`,pi=`hide${ui}`,mi=`hidden${ui}`,gi=`click${ui}.data-api`,_i="show",bi="collapse",vi="collapsing",yi=`:scope .${bi} .${bi}`,wi='[data-bs-toggle="collapse"]',Ai={parent:null,toggle:!0},Ei={parent:"(null|element)",toggle:"boolean"};class Ti extends we{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const i=Ee.find(wi);for(const t of i){const e=Ee.getSelectorFromElement(t),i=Ee.find(e).filter((t=>t===this._element));null!==e&&i.length&&this._triggerArray.push(t)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Ai}static get DefaultType(){return Ei}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter((t=>t!==this._element)).map((t=>Ti.getOrCreateInstance(t,{toggle:!1})))),t.length&&t[0]._isTransitioning)return;if(me.trigger(this._element,di).defaultPrevented)return;for(const e of t)e.hide();const e=this._getDimension();this._element.classList.remove(bi),this._element.classList.add(vi),this._element.style[e]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const i=`scroll${e[0].toUpperCase()+e.slice(1)}`;this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(vi),this._element.classList.add(bi,_i),this._element.style[e]="",me.trigger(this._element,fi)}),this._element,!0),this._element.style[e]=`${this._element[i]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(me.trigger(this._element,pi).defaultPrevented)return;const t=this._getDimension();this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,Kt(this._element),this._element.classList.add(vi),this._element.classList.remove(bi,_i);for(const t of this._triggerArray){const e=Ee.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[t]="",this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(vi),this._element.classList.add(bi),me.trigger(this._element,mi)}),this._element,!0)}_isShown(t=this._element){return t.classList.contains(_i)}_configAfterMerge(t){return t.toggle=Boolean(t.toggle),t.parent=Bt(t.parent),t}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(wi);for(const e of t){const t=Ee.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(t){const e=Ee.find(yi,this._config.parent);return Ee.find(t,this._config.parent).filter((t=>!e.includes(t)))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const i of t)i.classList.toggle("collapsed",!e),i.setAttribute("aria-expanded",e)}static jQueryInterface(t){const e={};return"string"==typeof t&&/show|hide/.test(t)&&(e.toggle=!1),this.each((function(){const i=Ti.getOrCreateInstance(this,e);if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t]()}}))}}me.on(document,gi,wi,(function(t){("A"===t.target.tagName||t.delegateTarget&&"A"===t.delegateTarget.tagName)&&t.preventDefault();for(const t of Ee.getMultipleElementsFromSelector(this))Ti.getOrCreateInstance(t,{toggle:!1}).toggle()})),Yt(Ti);const Ci="dropdown",Oi=".bs.dropdown",xi=".data-api",ki="ArrowUp",Si="ArrowDown",Li=`hide${Oi}`,$i=`hidden${Oi}`,Di=`show${Oi}`,Ii=`shown${Oi}`,Pi=`click${Oi}${xi}`,Ni=`keydown${Oi}${xi}`,Mi=`keyup${Oi}${xi}`,ji="show",Fi='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',Hi=`${Fi}.${ji}`,zi=".dropdown-menu",Bi=Xt()?"top-end":"top-start",Wi=Xt()?"top-start":"top-end",qi=Xt()?"bottom-end":"bottom-start",Ri=Xt()?"bottom-start":"bottom-end",Vi=Xt()?"left-start":"right-start",Ki=Xt()?"right-start":"left-start",Ui={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},Qi={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class Xi extends we{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=Ee.next(this._element,zi)[0]||Ee.prev(this._element,zi)[0]||Ee.findOne(zi,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Ui}static get DefaultType(){return Qi}static get NAME(){return Ci}toggle(){return this._isShown()?this.hide():this.show()}show(){if(qt(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!me.trigger(this._element,Di,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const t of[].concat(...document.body.children))me.on(t,"mouseover",Vt);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(ji),this._element.classList.add(ji),me.trigger(this._element,Ii,t)}}hide(){if(qt(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!me.trigger(this._element,Li,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))me.off(t,"mouseover",Vt);this._popper&&this._popper.destroy(),this._menu.classList.remove(ji),this._element.classList.remove(ji),this._element.setAttribute("aria-expanded","false"),ve.removeDataAttribute(this._menu,"popper"),me.trigger(this._element,$i,t)}}_getConfig(t){if("object"==typeof(t=super._getConfig(t)).reference&&!zt(t.reference)&&"function"!=typeof t.reference.getBoundingClientRect)throw new TypeError(`${Ci.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(void 0===Pt)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)");let t=this._element;"parent"===this._config.reference?t=this._parent:zt(this._config.reference)?t=Bt(this._config.reference):"object"==typeof this._config.reference&&(t=this._config.reference);const e=this._getPopperConfig();this._popper=It(t,this._menu,e)}_isShown(){return this._menu.classList.contains(ji)}_getPlacement(){const t=this._parent;if(t.classList.contains("dropend"))return Vi;if(t.classList.contains("dropstart"))return Ki;if(t.classList.contains("dropup-center"))return"top";if(t.classList.contains("dropdown-center"))return"bottom";const e="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return t.classList.contains("dropup")?e?Wi:Bi:e?Ri:qi}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(ve.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...Gt(this._config.popperConfig,[void 0,t])}}_selectMenuItem({key:t,target:e}){const i=Ee.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter((t=>Wt(t)));i.length&&Zt(i,e,t===Si,!i.includes(e)).focus()}static jQueryInterface(t){return this.each((function(){const e=Xi.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}static clearMenus(t){if(2===t.button||"keyup"===t.type&&"Tab"!==t.key)return;const e=Ee.find(Hi);for(const i of e){const e=Xi.getInstance(i);if(!e||!1===e._config.autoClose)continue;const n=t.composedPath(),s=n.includes(e._menu);if(n.includes(e._element)||"inside"===e._config.autoClose&&!s||"outside"===e._config.autoClose&&s)continue;if(e._menu.contains(t.target)&&("keyup"===t.type&&"Tab"===t.key||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const o={relatedTarget:e._element};"click"===t.type&&(o.clickEvent=t),e._completeHide(o)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),i="Escape"===t.key,n=[ki,Si].includes(t.key);if(!n&&!i)return;if(e&&!i)return;t.preventDefault();const s=this.matches(Fi)?this:Ee.prev(this,Fi)[0]||Ee.next(this,Fi)[0]||Ee.findOne(Fi,t.delegateTarget.parentNode),o=Xi.getOrCreateInstance(s);if(n)return t.stopPropagation(),o.show(),void o._selectMenuItem(t);o._isShown()&&(t.stopPropagation(),o.hide(),s.focus())}}me.on(document,Ni,Fi,Xi.dataApiKeydownHandler),me.on(document,Ni,zi,Xi.dataApiKeydownHandler),me.on(document,Pi,Xi.clearMenus),me.on(document,Mi,Xi.clearMenus),me.on(document,Pi,Fi,(function(t){t.preventDefault(),Xi.getOrCreateInstance(this).toggle()})),Yt(Xi);const Yi="backdrop",Gi="show",Ji=`mousedown.bs.${Yi}`,Zi={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},tn={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class en extends ye{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return Zi}static get DefaultType(){return tn}static get NAME(){return Yi}show(t){if(!this._config.isVisible)return void Gt(t);this._append();const e=this._getElement();this._config.isAnimated&&Kt(e),e.classList.add(Gi),this._emulateAnimation((()=>{Gt(t)}))}hide(t){this._config.isVisible?(this._getElement().classList.remove(Gi),this._emulateAnimation((()=>{this.dispose(),Gt(t)}))):Gt(t)}dispose(){this._isAppended&&(me.off(this._element,Ji),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add("fade"),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=Bt(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),me.on(t,Ji,(()=>{Gt(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(t){Jt(t,this._getElement(),this._config.isAnimated)}}const nn=".bs.focustrap",sn=`focusin${nn}`,on=`keydown.tab${nn}`,rn="backward",an={autofocus:!0,trapElement:null},ln={autofocus:"boolean",trapElement:"element"};class cn extends ye{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return an}static get DefaultType(){return ln}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),me.off(document,nn),me.on(document,sn,(t=>this._handleFocusin(t))),me.on(document,on,(t=>this._handleKeydown(t))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,me.off(document,nn))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const i=Ee.focusableChildren(e);0===i.length?e.focus():this._lastTabNavDirection===rn?i[i.length-1].focus():i[0].focus()}_handleKeydown(t){"Tab"===t.key&&(this._lastTabNavDirection=t.shiftKey?rn:"forward")}}const hn=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",un=".sticky-top",dn="padding-right",fn="margin-right";class pn{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,dn,(e=>e+t)),this._setElementAttributes(hn,dn,(e=>e+t)),this._setElementAttributes(un,fn,(e=>e-t))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,dn),this._resetElementAttributes(hn,dn),this._resetElementAttributes(un,fn)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,i){const n=this.getWidth();this._applyManipulationCallback(t,(t=>{if(t!==this._element&&window.innerWidth>t.clientWidth+n)return;this._saveInitialAttribute(t,e);const s=window.getComputedStyle(t).getPropertyValue(e);t.style.setProperty(e,`${i(Number.parseFloat(s))}px`)}))}_saveInitialAttribute(t,e){const i=t.style.getPropertyValue(e);i&&ve.setDataAttribute(t,e,i)}_resetElementAttributes(t,e){this._applyManipulationCallback(t,(t=>{const i=ve.getDataAttribute(t,e);null!==i?(ve.removeDataAttribute(t,e),t.style.setProperty(e,i)):t.style.removeProperty(e)}))}_applyManipulationCallback(t,e){if(zt(t))e(t);else for(const i of Ee.find(t,this._element))e(i)}}const mn=".bs.modal",gn=`hide${mn}`,_n=`hidePrevented${mn}`,bn=`hidden${mn}`,vn=`show${mn}`,yn=`shown${mn}`,wn=`resize${mn}`,An=`click.dismiss${mn}`,En=`mousedown.dismiss${mn}`,Tn=`keydown.dismiss${mn}`,Cn=`click${mn}.data-api`,On="modal-open",xn="show",kn="modal-static",Sn={backdrop:!0,focus:!0,keyboard:!0},Ln={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class $n extends we{constructor(t,e){super(t,e),this._dialog=Ee.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new pn,this._addEventListeners()}static get Default(){return Sn}static get DefaultType(){return Ln}static get NAME(){return"modal"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||me.trigger(this._element,vn,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(On),this._adjustDialog(),this._backdrop.show((()=>this._showElement(t))))}hide(){this._isShown&&!this._isTransitioning&&(me.trigger(this._element,gn).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(xn),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated())))}dispose(){me.off(window,mn),me.off(this._dialog,mn),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new en({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new cn({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=Ee.findOne(".modal-body",this._dialog);e&&(e.scrollTop=0),Kt(this._element),this._element.classList.add(xn),this._queueCallback((()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,me.trigger(this._element,yn,{relatedTarget:t})}),this._dialog,this._isAnimated())}_addEventListeners(){me.on(this._element,Tn,(t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())})),me.on(window,wn,(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),me.on(this._element,En,(t=>{me.one(this._element,An,(e=>{this._element===t.target&&this._element===e.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}))}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove(On),this._resetAdjustments(),this._scrollBar.reset(),me.trigger(this._element,bn)}))}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(me.trigger(this._element,_n).defaultPrevented)return;const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._element.style.overflowY;"hidden"===e||this._element.classList.contains(kn)||(t||(this._element.style.overflowY="hidden"),this._element.classList.add(kn),this._queueCallback((()=>{this._element.classList.remove(kn),this._queueCallback((()=>{this._element.style.overflowY=e}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),i=e>0;if(i&&!t){const t=Xt()?"paddingLeft":"paddingRight";this._element.style[t]=`${e}px`}if(!i&&t){const t=Xt()?"paddingRight":"paddingLeft";this._element.style[t]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each((function(){const i=$n.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t](e)}}))}}me.on(document,Cn,'[data-bs-toggle="modal"]',(function(t){const e=Ee.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&t.preventDefault(),me.one(e,vn,(t=>{t.defaultPrevented||me.one(e,bn,(()=>{Wt(this)&&this.focus()}))}));const i=Ee.findOne(".modal.show");i&&$n.getInstance(i).hide(),$n.getOrCreateInstance(e).toggle(this)})),Te($n),Yt($n);const Dn=".bs.offcanvas",In=".data-api",Pn=`load${Dn}${In}`,Nn="show",Mn="showing",jn="hiding",Fn=".offcanvas.show",Hn=`show${Dn}`,zn=`shown${Dn}`,Bn=`hide${Dn}`,Wn=`hidePrevented${Dn}`,qn=`hidden${Dn}`,Rn=`resize${Dn}`,Vn=`click${Dn}${In}`,Kn=`keydown.dismiss${Dn}`,Un={backdrop:!0,keyboard:!0,scroll:!1},Qn={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Xn extends we{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Un}static get DefaultType(){return Qn}static get NAME(){return"offcanvas"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||me.trigger(this._element,Hn,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||(new pn).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Mn),this._queueCallback((()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(Nn),this._element.classList.remove(Mn),me.trigger(this._element,zn,{relatedTarget:t})}),this._element,!0))}hide(){this._isShown&&(me.trigger(this._element,Bn).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(jn),this._backdrop.hide(),this._queueCallback((()=>{this._element.classList.remove(Nn,jn),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new pn).reset(),me.trigger(this._element,qn)}),this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=Boolean(this._config.backdrop);return new en({className:"offcanvas-backdrop",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?()=>{"static"!==this._config.backdrop?this.hide():me.trigger(this._element,Wn)}:null})}_initializeFocusTrap(){return new cn({trapElement:this._element})}_addEventListeners(){me.on(this._element,Kn,(t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():me.trigger(this._element,Wn))}))}static jQueryInterface(t){return this.each((function(){const e=Xn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}me.on(document,Vn,'[data-bs-toggle="offcanvas"]',(function(t){const e=Ee.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),qt(this))return;me.one(e,qn,(()=>{Wt(this)&&this.focus()}));const i=Ee.findOne(Fn);i&&i!==e&&Xn.getInstance(i).hide(),Xn.getOrCreateInstance(e).toggle(this)})),me.on(window,Pn,(()=>{for(const t of Ee.find(Fn))Xn.getOrCreateInstance(t).show()})),me.on(window,Rn,(()=>{for(const t of Ee.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(t).position&&Xn.getOrCreateInstance(t).hide()})),Te(Xn),Yt(Xn);const Yn={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Gn=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Jn=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Zn=(t,e)=>{const i=t.nodeName.toLowerCase();return e.includes(i)?!Gn.has(i)||Boolean(Jn.test(t.nodeValue)):e.filter((t=>t instanceof RegExp)).some((t=>t.test(i)))},ts={allowList:Yn,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},es={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},is={entry:"(string|element|function|null)",selector:"(string|element)"};class ns extends ye{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return ts}static get DefaultType(){return es}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map((t=>this._resolvePossibleFunction(t))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[e,i]of Object.entries(this._config.content))this._setContent(t,i,e);const e=t.children[0],i=this._resolvePossibleFunction(this._config.extraClass);return i&&e.classList.add(...i.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,i]of Object.entries(t))super._typeCheckConfig({selector:e,entry:i},is)}_setContent(t,e,i){const n=Ee.findOne(i,t);n&&((e=this._resolvePossibleFunction(e))?zt(e)?this._putElementInTemplate(Bt(e),n):this._config.html?n.innerHTML=this._maybeSanitize(e):n.textContent=e:n.remove())}_maybeSanitize(t){return this._config.sanitize?function(t,e,i){if(!t.length)return t;if(i&&"function"==typeof i)return i(t);const n=(new window.DOMParser).parseFromString(t,"text/html"),s=[].concat(...n.body.querySelectorAll("*"));for(const t of s){const i=t.nodeName.toLowerCase();if(!Object.keys(e).includes(i)){t.remove();continue}const n=[].concat(...t.attributes),s=[].concat(e["*"]||[],e[i]||[]);for(const e of n)Zn(e,s)||t.removeAttribute(e.nodeName)}return n.body.innerHTML}(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return Gt(t,[void 0,this])}_putElementInTemplate(t,e){if(this._config.html)return e.innerHTML="",void e.append(t);e.textContent=t.textContent}}const ss=new Set(["sanitize","allowList","sanitizeFn"]),os="fade",rs="show",as=".tooltip-inner",ls=".modal",cs="hide.bs.modal",hs="hover",us="focus",ds={AUTO:"auto",TOP:"top",RIGHT:Xt()?"left":"right",BOTTOM:"bottom",LEFT:Xt()?"right":"left"},fs={allowList:Yn,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},ps={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class ms extends we{constructor(t,e){if(void 0===Pt)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return fs}static get DefaultType(){return ps}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),me.off(this._element.closest(ls),cs,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const t=me.trigger(this._element,this.constructor.eventName("show")),e=(Rt(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!e)return;this._disposePopper();const i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));const{container:n}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(n.append(i),me.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(i),i.classList.add(rs),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))me.on(t,"mouseover",Vt);this._queueCallback((()=>{me.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1}),this.tip,this._isAnimated())}hide(){if(this._isShown()&&!me.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(rs),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))me.off(t,"mouseover",Vt);this._activeTrigger.click=!1,this._activeTrigger[us]=!1,this._activeTrigger[hs]=!1,this._isHovered=null,this._queueCallback((()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),me.trigger(this._element,this.constructor.eventName("hidden")))}),this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(os,rs),e.classList.add(`bs-${this.constructor.NAME}-auto`);const i=(t=>{do{t+=Math.floor(1e6*Math.random())}while(document.getElementById(t));return t})(this.constructor.NAME).toString();return e.setAttribute("id",i),this._isAnimated()&&e.classList.add(os),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new ns({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[as]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(os)}_isShown(){return this.tip&&this.tip.classList.contains(rs)}_createPopper(t){const e=Gt(this._config.placement,[this,t,this._element]),i=ds[e.toUpperCase()];return It(this._element,t,this._getPopperConfig(i))}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_resolvePossibleFunction(t){return Gt(t,[this._element,this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:t=>{this._getTipElement().setAttribute("data-popper-placement",t.state.placement)}}]};return{...e,...Gt(this._config.popperConfig,[void 0,e])}}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if("click"===e)me.on(this._element,this.constructor.eventName("click"),this._config.selector,(t=>{this._initializeOnDelegatedTarget(t).toggle()}));else if("manual"!==e){const t=e===hs?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),i=e===hs?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");me.on(this._element,t,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusin"===t.type?us:hs]=!0,e._enter()})),me.on(this._element,i,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusout"===t.type?us:hs]=e._element.contains(t.relatedTarget),e._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},me.on(this._element.closest(ls),cs,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=ve.getDataAttributes(this._element);for(const t of Object.keys(e))ss.has(t)&&delete e[t];return t={...e,..."object"==typeof t&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=!1===t.container?document.body:Bt(t.container),"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,i]of Object.entries(this._config))this.constructor.Default[e]!==i&&(t[e]=i);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each((function(){const e=ms.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}Yt(ms);const gs=".popover-header",_s=".popover-body",bs={...ms.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},vs={...ms.DefaultType,content:"(null|string|element|function)"};class ys extends ms{static get Default(){return bs}static get DefaultType(){return vs}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[gs]:this._getTitle(),[_s]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each((function(){const e=ys.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}Yt(ys);const ws=".bs.scrollspy",As=`activate${ws}`,Es=`click${ws}`,Ts=`load${ws}.data-api`,Cs="active",Os="[href]",xs=".nav-link",ks=`${xs}, .nav-item > ${xs}, .list-group-item`,Ss={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Ls={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class $s extends we{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Ss}static get DefaultType(){return Ls}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=Bt(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,"string"==typeof t.threshold&&(t.threshold=t.threshold.split(",").map((t=>Number.parseFloat(t)))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(me.off(this._config.target,Es),me.on(this._config.target,Es,Os,(t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const i=this._rootElement||window,n=e.offsetTop-this._element.offsetTop;if(i.scrollTo)return void i.scrollTo({top:n,behavior:"smooth"});i.scrollTop=n}})))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((t=>this._observerCallback(t)),t)}_observerCallback(t){const e=t=>this._targetLinks.get(`#${t.target.id}`),i=t=>{this._previousScrollData.visibleEntryTop=t.target.offsetTop,this._process(e(t))},n=(this._rootElement||document.documentElement).scrollTop,s=n>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=n;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(o));continue}const t=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(s&&t){if(i(o),!n)return}else s||t||i(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=Ee.find(Os,this._config.target);for(const e of t){if(!e.hash||qt(e))continue;const t=Ee.findOne(decodeURI(e.hash),this._element);Wt(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(Cs),this._activateParents(t),me.trigger(this._element,As,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains("dropdown-item"))Ee.findOne(".dropdown-toggle",t.closest(".dropdown")).classList.add(Cs);else for(const e of Ee.parents(t,".nav, .list-group"))for(const t of Ee.prev(e,ks))t.classList.add(Cs)}_clearActiveClass(t){t.classList.remove(Cs);const e=Ee.find(`${Os}.${Cs}`,t);for(const t of e)t.classList.remove(Cs)}static jQueryInterface(t){return this.each((function(){const e=$s.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}me.on(window,Ts,(()=>{for(const t of Ee.find('[data-bs-spy="scroll"]'))$s.getOrCreateInstance(t)})),Yt($s);const Ds=".bs.tab",Is=`hide${Ds}`,Ps=`hidden${Ds}`,Ns=`show${Ds}`,Ms=`shown${Ds}`,js=`click${Ds}`,Fs=`keydown${Ds}`,Hs=`load${Ds}`,zs="ArrowLeft",Bs="ArrowRight",Ws="ArrowUp",qs="ArrowDown",Rs="Home",Vs="End",Ks="active",Us="fade",Qs="show",Xs=".dropdown-toggle",Ys=`:not(${Xs})`,Gs='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Js=`.nav-link${Ys}, .list-group-item${Ys}, [role="tab"]${Ys}, ${Gs}`,Zs=`.${Ks}[data-bs-toggle="tab"], .${Ks}[data-bs-toggle="pill"], .${Ks}[data-bs-toggle="list"]`;class to extends we{constructor(t){super(t),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),me.on(this._element,Fs,(t=>this._keydown(t))))}static get NAME(){return"tab"}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),i=e?me.trigger(e,Is,{relatedTarget:t}):null;me.trigger(t,Ns,{relatedTarget:e}).defaultPrevented||i&&i.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){t&&(t.classList.add(Ks),this._activate(Ee.getElementFromSelector(t)),this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),me.trigger(t,Ms,{relatedTarget:e})):t.classList.add(Qs)}),t,t.classList.contains(Us)))}_deactivate(t,e){t&&(t.classList.remove(Ks),t.blur(),this._deactivate(Ee.getElementFromSelector(t)),this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),me.trigger(t,Ps,{relatedTarget:e})):t.classList.remove(Qs)}),t,t.classList.contains(Us)))}_keydown(t){if(![zs,Bs,Ws,qs,Rs,Vs].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=this._getChildren().filter((t=>!qt(t)));let i;if([Rs,Vs].includes(t.key))i=e[t.key===Rs?0:e.length-1];else{const n=[Bs,qs].includes(t.key);i=Zt(e,t.target,n,!0)}i&&(i.focus({preventScroll:!0}),to.getOrCreateInstance(i).show())}_getChildren(){return Ee.find(Js,this._parent)}_getActiveElem(){return this._getChildren().find((t=>this._elemIsActive(t)))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(const t of e)this._setInitialAttributesOnChild(t)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),i=this._getOuterElement(t);t.setAttribute("aria-selected",e),i!==t&&this._setAttributeIfNotExists(i,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=Ee.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(e,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,e){const i=this._getOuterElement(t);if(!i.classList.contains("dropdown"))return;const n=(t,n)=>{const s=Ee.findOne(t,i);s&&s.classList.toggle(n,e)};n(Xs,Ks),n(".dropdown-menu",Qs),i.setAttribute("aria-expanded",e)}_setAttributeIfNotExists(t,e,i){t.hasAttribute(e)||t.setAttribute(e,i)}_elemIsActive(t){return t.classList.contains(Ks)}_getInnerElement(t){return t.matches(Js)?t:Ee.findOne(Js,t)}_getOuterElement(t){return t.closest(".nav-item, .list-group-item")||t}static jQueryInterface(t){return this.each((function(){const e=to.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}me.on(document,js,Gs,(function(t){["A","AREA"].includes(this.tagName)&&t.preventDefault(),qt(this)||to.getOrCreateInstance(this).show()})),me.on(window,Hs,(()=>{for(const t of Ee.find(Zs))to.getOrCreateInstance(t)})),Yt(to);const eo=".bs.toast",io=`mouseover${eo}`,no=`mouseout${eo}`,so=`focusin${eo}`,oo=`focusout${eo}`,ro=`hide${eo}`,ao=`hidden${eo}`,lo=`show${eo}`,co=`shown${eo}`,ho="hide",uo="show",fo="showing",po={animation:"boolean",autohide:"boolean",delay:"number"},mo={animation:!0,autohide:!0,delay:5e3};class go extends we{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return mo}static get DefaultType(){return po}static get NAME(){return"toast"}show(){me.trigger(this._element,lo).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(ho),Kt(this._element),this._element.classList.add(uo,fo),this._queueCallback((()=>{this._element.classList.remove(fo),me.trigger(this._element,co),this._maybeScheduleHide()}),this._element,this._config.animation))}hide(){this.isShown()&&(me.trigger(this._element,ro).defaultPrevented||(this._element.classList.add(fo),this._queueCallback((()=>{this._element.classList.add(ho),this._element.classList.remove(fo,uo),me.trigger(this._element,ao)}),this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(uo),super.dispose()}isShown(){return this._element.classList.contains(uo)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay)))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=e;break;case"focusin":case"focusout":this._hasKeyboardInteraction=e}if(e)return void this._clearTimeout();const i=t.relatedTarget;this._element===i||this._element.contains(i)||this._maybeScheduleHide()}_setListeners(){me.on(this._element,io,(t=>this._onInteraction(t,!0))),me.on(this._element,no,(t=>this._onInteraction(t,!1))),me.on(this._element,so,(t=>this._onInteraction(t,!0))),me.on(this._element,oo,(t=>this._onInteraction(t,!1)))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each((function(){const e=go.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}Te(go),Yt(go);const _o=Object.freeze(Object.defineProperty({__proto__:null,Alert:ke,Button:Le,Carousel:hi,Collapse:Ti,Dropdown:Xi,Modal:$n,Offcanvas:Xn,Popover:ys,ScrollSpy:$s,Tab:to,Toast:go,Tooltip:ms},Symbol.toStringTag,{value:"Module"}));[].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]')).map((function(t){let e={boundary:"viewport"===t.getAttribute("data-bs-boundary")?document.querySelector(".btn"):"clippingParents"};return new Xi(t,e)})),[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map((function(t){let e={delay:{show:50,hide:50},html:"true"===t.getAttribute("data-bs-html")??!1,placement:t.getAttribute("data-bs-placement")??"auto"};return new ms(t,e)})),[].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]')).map((function(t){let e={delay:{show:50,hide:50},html:"true"===t.getAttribute("data-bs-html")??!1,placement:t.getAttribute("data-bs-placement")??"auto"};return new ys(t,e)})),[].slice.call(document.querySelectorAll('[data-bs-toggle="switch-icon"]')).map((function(t){t.addEventListener("click",(e=>{e.stopPropagation(),t.classList.toggle("active")}))})),(()=>{const t=window.location.hash;t&&[].slice.call(document.querySelectorAll('[data-bs-toggle="tab"]')).filter((e=>e.hash===t)).map((t=>{new to(t).show()}))})(),[].slice.call(document.querySelectorAll('[data-bs-toggle="toast"]')).map((function(t){if(!t.hasAttribute("data-bs-target"))return;const e=new go(t.getAttribute("data-bs-target"));t.addEventListener("click",(()=>{e.show()}))}));const bo="tblr-",vo=(t,e)=>{const i=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);return i?`rgba(${parseInt(i[1],16)}, ${parseInt(i[2],16)}, ${parseInt(i[3],16)}, ${e})`:null},yo=Object.freeze(Object.defineProperty({__proto__:null,getColor:(t,e=1)=>{const i=getComputedStyle(document.body).getPropertyValue(`--${bo}${t}`).trim();return 1!==e?vo(i,e):i},hexToRgba:vo,prefix:bo},Symbol.toStringTag,{value:"Module"}));t.Alert=ke,t.Button=Le,t.Carousel=hi,t.Collapse=Ti,t.Dropdown=Xi,t.Modal=$n,t.Offcanvas=Xn,t.Popover=ys,t.ScrollSpy=$s,t.Tab=to,t.Toast=go,t.Tooltip=ms,t.bootstrap=_o,t.tabler=yo,Object.defineProperty(t,Symbol.toStringTag,{value:"Module"})}));
var bootstrap = tabler.bootstrap;