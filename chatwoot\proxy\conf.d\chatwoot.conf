# Replace with your real domain and certificate files
map $http_upgrade $connection_upgrade {
  default upgrade;
  ''      close;
}

server {
  listen 80;
  server_name chatw.uniqsolutions.com.br;
  return 301 https://$host$request_uri;
}

server {
  listen 443 ssl http2;
  server_name chatw.uniqsolutions.com.br;

  ssl_certificate     /etc/nginx/certs/fullchain.pem;
  ssl_certificate_key /etc/nginx/certs/privkey.pem;

  # Security headers
  add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
  add_header X-Frame-Options SAMEORIGIN;
  add_header X-Content-Type-Options nosniff;
  add_header Referrer-Policy no-referrer-when-downgrade;
  add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: https: wss:; connect-src 'self' https: wss:; img-src 'self' data: https:; font-src 'self' data: https:; frame-ancestors 'self';";

  # Proxy to Chatwoot rails service in the same Docker network
  location / {
    proxy_pass http://chatwoot-rails-1:3000;
    proxy_http_version 1.1;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    # WebSockets
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection $connection_upgrade;

    # Timeouts
    proxy_connect_timeout 60s;
    proxy_read_timeout 60s;
    proxy_send_timeout 60s;
  }

  # Static files (optional micro-cache)
  location ~* \.(?:css|js|jpg|jpeg|gif|png|svg|ico)$ {
    expires 7d;
    access_log off;
    proxy_pass http://chatwoot-rails-1:3000;
  }
}
