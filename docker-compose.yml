services:
  postgres:
    image: pgvector/pgvector:pg15
    container_name: uniforms-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: uniforms_survey
      POSTGRES_USER: uniforms_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-uniforms_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "127.0.0.1:${POSTGRES_PORT:-6100}:5432"
    networks:
      - uniforms-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U uniforms_user -d uniforms_survey"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Redis para cache e sessões
  redis:
    image: redis:7-alpine
    container_name: uniforms-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "127.0.0.1:${REDIS_PORT:-6101}:6379"
    networks:
      - uniforms-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Backend Node.js - Sistema de Pesquisas e Webhooks
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
      args:
        - NODE_ENV=${NODE_ENV:-production}
    container_name: uniforms-backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 3000
      DATABASE_URL: ${DATABASE_URL:-**********************************************************/uniforms_survey}
      # Used by entrypoint wait
      DB_HOST: ${DB_HOST:-postgres}
      DB_PORT: ${DB_PORT:-5432}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-this}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-7d}
      REDIS_URL: redis://redis:6379
      # URLs externas - configurar com seus serviços
      EXTERNAL_N8N_WEBHOOK_URL: ${EXTERNAL_N8N_WEBHOOK_URL:-http://your-n8n-server.com}
      EXTERNAL_EVOLUTION_API_URL: ${EXTERNAL_EVOLUTION_API_URL:-http://your-evolution-server.com}
      EXTERNAL_EVOLUTION_API_KEY: ${EXTERNAL_EVOLUTION_API_KEY:-your-evolution-api-key}
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost:6104}
      BACKEND_URL: ${BACKEND_URL:-http://localhost:6103}
      # Control auto-seed on start
      SEED_ON_START: ${SEED_ON_START:-true}
      # Optional seed overrides
      ADMIN_DEFAULT_PASSWORD: ${ADMIN_DEFAULT_PASSWORD:-Adm1n!234567}
      USER_DEFAULT_PASSWORD: ${USER_DEFAULT_PASSWORD:-Tes!e123456}
      SEED_RESET_ADMIN_PASSWORD: ${SEED_RESET_ADMIN_PASSWORD:-false}
    ports:
      - "127.0.0.1:${BACKEND_PORT:-6103}:3000"
    networks:
      - uniforms-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./uploads:/app/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # ChatWOOT - Gerenciamento de Conversas WhatsApp
  chatwoot-web:
    image: chatwoot/chatwoot:latest
    container_name: uniforms-chatwoot-web
    restart: unless-stopped
    command: ["/bin/sh", "-lc", "rm -f /app/tmp/pids/server.pid && bundle exec rails db:chatwoot_prepare && bundle exec rails server -b 0.0.0.0 -p 3000"]
    environment:
      - NODE_ENV=production
      - RAILS_ENV=production
      - INSTALLATION_ENV=docker
      - SECRET_KEY_BASE=${CHATWOOT_SECRET_KEY_BASE:-your-secret-key-base-change-this}
      - FRONTEND_URL=http://localhost:6104
      - DEFAULT_LOCALE=pt_BR
      - FORCE_SSL=false
      - ENABLE_ACCOUNT_SIGNUP=false
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DATABASE=chatwoot_production
      - POSTGRES_USERNAME=uniforms_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-uniforms_password}
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=
      - REDIS_SENTINELS=
      - REDIS_SENTINEL_MASTER_NAME=
      # Configurações de email (opcional)
      - MAILER_SENDER_EMAIL=<EMAIL>
      - SMTP_DOMAIN=uniforms.local
      - SMTP_ADDRESS=
      - SMTP_PORT=587
      - SMTP_USERNAME=
      - SMTP_PASSWORD=
      - SMTP_AUTHENTICATION=plain
      - SMTP_ENABLE_STARTTLS_AUTO=true
      # Configurações de storage (opcional)
      - ACTIVE_STORAGE_SERVICE=local
      # Configurações de SSO
      - ENABLE_SENTRY=false
      - RAILS_LOG_TO_STDOUT=true
      - RAILS_SERVE_STATIC_FILES=true
    ports:
      - "127.0.0.1:${CHATWOOT_PORT:-6300}:3000"
    networks:
      - uniforms-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - chatwoot_data:/app/storage

  # ChatWOOT Sidekiq - Worker para processamento de jobs
  chatwoot-sidekiq:
    image: chatwoot/chatwoot:latest
    container_name: uniforms-chatwoot-sidekiq
    restart: unless-stopped
    command: ["/bin/sh", "-lc", "bundle exec rails db:chatwoot_prepare && bundle exec sidekiq -C config/sidekiq.yml"]
    environment:
      - NODE_ENV=production
      - RAILS_ENV=production
      - INSTALLATION_ENV=docker
      - SECRET_KEY_BASE=${CHATWOOT_SECRET_KEY_BASE:-your-secret-key-base-change-this}
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DATABASE=chatwoot_production
      - POSTGRES_USERNAME=uniforms_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-uniforms_password}
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=
      - ACTIVE_STORAGE_SERVICE=local
    networks:
      - uniforms-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - chatwoot_data:/app/storage

  # Frontend React - Interface Unificada (Pesquisas + ChatWOOT)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.simple
    container_name: uniforms-frontend
    restart: unless-stopped
    environment:
      - REACT_APP_API_URL=http://localhost:6103/api
      - REACT_APP_CHATWOOT_URL=http://localhost:${CHATWOOT_PORT:-6300}
      - REACT_APP_ENABLE_SSO=true
    ports:
      - "127.0.0.1:${FRONTEND_PORT:-6104}:80"
    networks:
      - uniforms-network
    depends_on:
      - backend
      - chatwoot-web
  volumes: []

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  chatwoot_data:
    driver: local

networks:
  uniforms-network:
    driver: bridge
